package services

import (
	"context"
	"fmt"

	"github.com/eldon111/impactresume/internal/models"
	"github.com/markbates/goth"
)

// AuthServiceInterface defines the interface for authentication operations
type AuthServiceInterface interface {
	HandleOAuthCallback(ctx context.Context, provider string, gothUser goth.User) (*models.User, error)
	GenerateAuthResponse(user *models.User) (*AuthResponse, error)
	RefreshUserToken(token string) (string, error)
}

// AuthResponse represents the authentication response
type AuthResponse struct {
	Token string      `json:"token"`
	User  models.User `json:"user"`
}

// AuthService handles authentication workflows
type AuthService struct {
	userService UserServiceInterface
	jwtService  *JWTService
}

// NewAuthService creates a new auth service instance
func NewAuthService(userService UserServiceInterface, jwtService *JWTService) *AuthService {
	return &AuthService{
		userService: userService,
		jwtService:  jwtService,
	}
}

// HandleOAuthCallback processes OAuth callback and creates/updates user
func (as *AuthService) HandleOAuthCallback(ctx context.Context, provider string, gothUser goth.User) (*models.User, error) {
	var user *models.User
	var err error

	switch provider {
	case "github":
		user, err = as.userService.CreateOrUpdateOAuthUser(ctx, provider, &models.GitHubOAuth{
			GitHubID:     gothUser.UserID,
			Username:     as.getGitHubUsername(gothUser),
			Email:        as.stringToPointer(gothUser.Email),
			AccessToken:  gothUser.AccessToken,
			RefreshToken: &gothUser.RefreshToken,
			AvatarURL:    &gothUser.AvatarURL,
		})
	case "google":
		user, err = as.userService.CreateOrUpdateOAuthUser(ctx, provider, &models.GoogleOAuth{
			GoogleID:     gothUser.UserID,
			Email:        gothUser.Email,
			AccessToken:  gothUser.AccessToken,
			RefreshToken: &gothUser.RefreshToken,
			AvatarURL:    &gothUser.AvatarURL,
		})
	case "linkedin":
		user, err = as.userService.CreateOrUpdateOAuthUser(ctx, provider, &models.LinkedInOAuth{
			LinkedInID:   gothUser.UserID,
			Email:        gothUser.Email,
			FirstName:    &gothUser.FirstName,
			LastName:     &gothUser.LastName,
			AccessToken:  gothUser.AccessToken,
			RefreshToken: &gothUser.RefreshToken,
			AvatarURL:    &gothUser.AvatarURL,
		})
	default:
		return nil, fmt.Errorf("unsupported OAuth provider: %s", provider)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create/update user for provider %s: %w", provider, err)
	}

	return user, nil
}

// GenerateAuthResponse creates an authentication response with JWT token
func (as *AuthService) GenerateAuthResponse(user *models.User) (*AuthResponse, error) {
	if user == nil {
		return nil, fmt.Errorf("user cannot be nil")
	}

	// Generate JWT token
	jwtToken, err := as.jwtService.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate JWT token: %w", err)
	}

	response := &AuthResponse{
		Token: jwtToken,
		User:  *user,
	}

	return response, nil
}

// RefreshUserToken refreshes a JWT token
func (as *AuthService) RefreshUserToken(token string) (string, error) {
	if token == "" {
		return "", fmt.Errorf("token cannot be empty")
	}

	newToken, err := as.jwtService.RefreshToken(token)
	if err != nil {
		return "", fmt.Errorf("failed to refresh token: %w", err)
	}

	return newToken, nil
}

// getGitHubUsername prioritizes GitHub user name fields in order: GitHub user name, first name, nickname
func (as *AuthService) getGitHubUsername(gothUser goth.User) string {
	if gothUser.NickName != "" {
		return gothUser.NickName
	}
	if gothUser.Name != "" {
		return gothUser.Name
	}
	if gothUser.FirstName != "" {
		return gothUser.FirstName
	}
	return gothUser.UserID // fallback to UserID if no username available
}

// stringToPointer converts a string to a pointer, returns nil for empty strings
func (as *AuthService) stringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
