package mocks

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impactresume/internal/database"
	"github.com/eldon111/impactresume/internal/models"
	"github.com/eldon111/impactresume/internal/services"
)

// MockJobService implements services.JobServiceInterface for testing
type MockJobService struct {
	shouldError bool
}

// NewMockJobService creates a new mock job service
func NewMockJobService() *MockJobService {
	return &MockJobService{shouldError: false}
}

// CreateJob mocks job creation with validation
func (m *MockJobService) CreateJob(ctx context.Context, userID int, request *models.JobCreateRequest) (*models.Job, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}

	// Perform basic validation to match expected test behavior
	if request.Title == "" {
		return nil, fmt.Errorf("validation failed: title is required")
	}
	if request.Company == "" {
		return nil, fmt.Errorf("validation failed: company is required")
	}
	if request.Description == "" {
		return nil, fmt.Errorf("validation failed: description is required")
	}
	if len(request.Title) > 200 {
		return nil, fmt.Errorf("validation failed: title too long")
	}

	return &models.Job{
		ID:          "job_123",
		UserID:      userID,
		Title:       request.Title,
		Company:     request.Company,
		Location:    request.Location,
		StartDate:   request.StartDate,
		EndDate:     request.EndDate,
		IsCurrent:   request.IsCurrent,
		Description: request.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

// GetJob mocks job retrieval
func (m *MockJobService) GetJob(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	return &models.Job{
		ID:          jobID,
		UserID:      userID,
		Title:       "Software Engineer",
		Company:     "Tech Corp",
		Location:    "San Francisco, CA",
		Description: "Develop software applications",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

// GetJobs mocks job listing
func (m *MockJobService) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	return []models.Job{
		{
			ID:          "job_1",
			UserID:      userID,
			Title:       "Software Engineer",
			Company:     "Tech Corp",
			Description: "Develop software",
		},
		{
			ID:          "job_2",
			UserID:      userID,
			Title:       "Senior Developer",
			Company:     "Another Corp",
			Description: "Lead development",
		},
	}, nil
}

// UpdateJobDescription mocks job description updates
func (m *MockJobService) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	if m.shouldError {
		return database.ErrJobNotFound
	}

	// Return not found for specific test job IDs
	if jobID == "nonexistent_job" {
		return database.ErrJobNotFound
	}

	// Perform basic validation to match expected test behavior
	if description == "" {
		return fmt.Errorf("validation failed: description is required")
	}
	if len(description) > 5000 {
		return fmt.Errorf("validation failed: description too long")
	}

	return nil
}

// DeleteJob mocks job deletion
func (m *MockJobService) DeleteJob(ctx context.Context, userID int, jobID string) error {
	if m.shouldError {
		return database.ErrJobNotFound
	}
	return nil
}

// ValidateJobRequest mocks job request validation
func (m *MockJobService) ValidateJobRequest(request *models.JobCreateRequest) error {
	if m.shouldError {
		return fmt.Errorf("validation failed")
	}
	return nil
}

// SetError configures the mock to return errors
func (m *MockJobService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// Ensure MockJobService implements the interface
var _ services.JobServiceInterface = (*MockJobService)(nil)
