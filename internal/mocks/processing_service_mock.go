package mocks

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impactresume/internal/database"
	"github.com/eldon111/impactresume/internal/models"
	"github.com/eldon111/impactresume/internal/services"
)

// MockProcessingService implements services.ProcessingServiceInterface for testing
type MockProcessingService struct {
	shouldError bool
}

// NewMockProcessingService creates a new mock processing service
func NewMockProcessingService() *MockProcessingService {
	return &MockProcessingService{shouldError: false}
}

// ProcessSingleJob mocks single job processing
func (m *MockProcessingService) ProcessSingleJob(ctx context.Context, userID int, jobID string) (*models.ProcessedJob, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	return &models.ProcessedJob{
		JobID:        jobID,
		BulletPoints: []string{"Enhanced software development process", "Improved team collaboration"},
		Model:        "claude-3-haiku",
		ProcessedAt:  time.Now(),
		TokensUsed:   150,
	}, nil
}

// ProcessMultipleJobs mocks multiple job processing
func (m *MockProcessingService) ProcessMultipleJobs(ctx context.Context, userID int, jobIDs []string) ([]models.ProcessedJob, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	
	// Perform basic validation to match expected test behavior
	if len(jobIDs) == 0 {
		return nil, fmt.Errorf("validation failed: job IDs list cannot be empty")
	}
	
	// Check for nonexistent jobs
	for _, jobID := range jobIDs {
		if jobID == "nonexistent_job" {
			return nil, database.ErrJobNotFound
		}
	}
	
	var processedJobs []models.ProcessedJob
	for _, jobID := range jobIDs {
		processedJobs = append(processedJobs, models.ProcessedJob{
			JobID:        jobID,
			BulletPoints: []string{"Enhanced software development process", "Improved team collaboration"},
			Model:        "claude-3-haiku",
			ProcessedAt:  time.Now(),
			TokensUsed:   150,
		})
	}
	return processedJobs, nil
}

// CreateProcessedJob mocks processed job creation
func (m *MockProcessingService) CreateProcessedJob(ctx context.Context, job *models.Job, result *services.JobCondensationResult) (*models.ProcessedJob, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	return &models.ProcessedJob{
		JobID:        job.ID,
		BulletPoints: result.BulletPoints,
		Model:        result.Model,
		ProcessedAt:  time.Now(),
		TokensUsed:   result.TokensUsed,
	}, nil
}

// ValidateProcessingRequest mocks processing request validation
func (m *MockProcessingService) ValidateProcessingRequest(request *models.JobProcessRequest) error {
	if m.shouldError {
		return fmt.Errorf("validation failed")
	}
	return nil
}

// SetError configures the mock to return errors
func (m *MockProcessingService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// Ensure MockProcessingService implements the interface
var _ services.ProcessingServiceInterface = (*MockProcessingService)(nil)
