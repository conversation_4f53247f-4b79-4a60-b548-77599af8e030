package mocks

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"github.com/eldon111/impactresume/internal/services"
	"github.com/markbates/goth"
)

// MockAuthService implements services.AuthServiceInterface for testing
type MockAuthService struct {
	shouldError bool
}

// NewMockAuthService creates a new mock auth service
func NewMockAuthService() *MockAuthService {
	return &MockAuthService{shouldError: false}
}

// HandleOAuthCallback mocks OAuth callback handling
func (m *MockAuthService) HandleOAuthCallback(ctx context.Context, provider string, gothUser goth.User) (*models.User, error) {
	if m.shouldError {
		return nil, fmt.Errorf("OAuth callback failed")
	}

	// Return a mock user based on provider
	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	switch provider {
	case "github":
		user.GitHubOAuth = &models.GitHubOAuth{
			UserID:      1,
			GitHubID:    gothUser.UserID,
			Username:    gothUser.NickName,
			Email:       &gothUser.Email,
			AccessToken: gothUser.AccessToken,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
	case "google":
		user.GoogleOAuth = &models.GoogleOAuth{
			UserID:      1,
			GoogleID:    gothUser.UserID,
			Email:       gothUser.Email,
			AccessToken: gothUser.AccessToken,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
	case "linkedin":
		user.LinkedInOAuth = &models.LinkedInOAuth{
			UserID:      1,
			LinkedInID:  gothUser.UserID,
			Email:       gothUser.Email,
			FirstName:   &gothUser.FirstName,
			LastName:    &gothUser.LastName,
			AccessToken: gothUser.AccessToken,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
	}

	return user, nil
}

// GenerateAuthResponse mocks auth response generation
func (m *MockAuthService) GenerateAuthResponse(user *models.User) (*services.AuthResponse, error) {
	if m.shouldError {
		return nil, fmt.Errorf("failed to generate auth response")
	}

	return &services.AuthResponse{
		Token: "mock_jwt_token_12345",
		User:  *user,
	}, nil
}

// RefreshUserToken mocks token refresh
func (m *MockAuthService) RefreshUserToken(token string) (string, error) {
	if m.shouldError {
		return "", fmt.Errorf("failed to refresh token")
	}

	if token == "" {
		return "", fmt.Errorf("token cannot be empty")
	}

	return "refreshed_mock_jwt_token_67890", nil
}

// SetError configures the mock to return errors
func (m *MockAuthService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// Ensure MockAuthService implements the interface
var _ services.AuthServiceInterface = (*MockAuthService)(nil)
