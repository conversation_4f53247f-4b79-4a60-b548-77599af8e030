package database

import (
	"context"
	"errors"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrJobNotFound = errors.New("job not found")
)

// JobRepository defines the interface for job database operations
type JobRepository interface {
	CreateJob(ctx context.Context, job *models.Job) error
	GetJobByID(ctx context.Context, userID int, id string) (*models.Job, error)
	GetJobs(ctx context.Context, userID int) ([]models.Job, error)
	UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error
	DeleteJob(ctx context.Context, userID int, jobID string) error
}

// MongoJobRepository implements JobRepository for MongoDB
type MongoJobRepository struct {
	database   *mongo.Database
	collection *mongo.Collection
}

// NewMongoJobRepository creates a new MongoDB job repository
func NewMongoJobRepository(database *mongo.Database) *MongoJobRepository {
	return &MongoJobRepository{
		database:   database,
		collection: database.Collection("jobs"),
	}
}

// CreateJob creates a new job
func (r *MongoJobRepository) CreateJob(ctx context.Context, job *models.Job) error {
	_, err := r.collection.InsertOne(ctx, job)
	return err
}

// GetJobByID retrieves a job by ID and user ID
func (r *MongoJobRepository) GetJobByID(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	var job models.Job
	err := r.collection.FindOne(ctx, filter).Decode(&job)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrJobNotFound
		}
		return nil, err
	}
	return &job, nil
}

// GetJobs retrieves all jobs for a user
func (r *MongoJobRepository) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	filter := bson.M{"userId": userID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var jobs []models.Job
	if err = cursor.All(ctx, &jobs); err != nil {
		return nil, err
	}
	return jobs, nil
}

// UpdateJobDescription updates a job's description
func (r *MongoJobRepository) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}
	update := bson.M{
		"$set": bson.M{
			"description": description,
			"updatedAt":   time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	if result.MatchedCount == 0 {
		return ErrJobNotFound
	}
	return nil
}

// DeleteJob deletes a job by ID and user ID
func (r *MongoJobRepository) DeleteJob(ctx context.Context, userID int, jobID string) error {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	if result.DeletedCount == 0 {
		return ErrJobNotFound
	}
	return nil
}
