package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/eldon111/impactresume/internal/mocks"
	"github.com/eldon111/impactresume/internal/models"
	"github.com/labstack/echo/v4"
)

// Helper function to create Echo context for testing
func createEchoContext(method, path string, body *bytes.Buffer, pathParams map[string]string) (echo.Context, *httptest.ResponseRecorder) {
	e := echo.New()

	var req *http.Request
	if body != nil {
		req = httptest.NewRequest(method, path, body)
	} else {
		req = httptest.NewRequest(method, path, nil)
	}

	if method == "POST" || method == "PUT" || method == "PATCH" {
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	}

	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// Set path parameters if provided
	if pathParams != nil {
		for key, value := range pathParams {
			c.Set<PERSON>aram<PERSON>ames(key)
			c.SetParamValues(value)
		}
	}

	// Mock user ID for testing (simulate authenticated user)
	c.Set("user_id", 1)

	return c, rec
}

// Helper function to handle Echo error responses in tests
func handleEchoTestResult(t *testing.T, err error, rec *httptest.ResponseRecorder, expectedStatus int, checkResponse func(t *testing.T, body []byte)) {
	if err != nil {
		// Echo returns HTTP errors, check if it's the expected error
		if echoErr, ok := err.(*echo.HTTPError); ok {
			if echoErr.Code != expectedStatus {
				t.Errorf("Expected status %d, got %d", expectedStatus, echoErr.Code)
			}
			// For error responses, create a mock response body for tests
			if checkResponse != nil {
				var errorResponse map[string]interface{}

				// Handle validation errors (arrays) vs simple string messages
				if validationErrors, ok := echoErr.Message.([]string); ok {
					// This is a validation error array
					errorResponse = map[string]interface{}{
						"error":   "Bad Request",
						"code":    echoErr.Code,
						"message": "Validation failed",
						"details": validationErrors,
					}
				} else {
					// This is a simple string message
					errorResponse = map[string]interface{}{
						"error":   "Bad Request",
						"code":    echoErr.Code,
						"message": echoErr.Message,
					}
				}

				if errorJSON, err := json.Marshal(errorResponse); err == nil {
					checkResponse(t, errorJSON)
				}
			}
		} else {
			t.Errorf("Expected Echo HTTP error, got %v", err)
		}
	} else {
		// No error, check response status
		if rec.Code != expectedStatus {
			t.Errorf("Expected status %d, got %d", expectedStatus, rec.Code)
		}
		if checkResponse != nil {
			checkResponse(t, rec.Body.Bytes())
		}
	}
}

func TestJobHandler_CreateJob(t *testing.T) {
	mockJobService := mocks.NewMockJobService()
	mockProcessingService := mocks.NewMockProcessingService()
	handler := NewJobHandler(mockJobService, mockProcessingService)

	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "Valid job creation",
			requestBody: `{
				"title": "Software Engineer",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications using Go and React",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusCreated,
			checkResponse: func(t *testing.T, body []byte) {
				// Parse as generic JSON to avoid DateOnly parsing issues in tests
				var result map[string]interface{}
				if err := json.Unmarshal(body, &result); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
					return
				}
				if result["title"] != "Software Engineer" {
					t.Errorf("Expected title 'Software Engineer', got '%v'", result["title"])
				}
				if result["company"] != "Tech Corp" {
					t.Errorf("Expected company 'Tech Corp', got '%v'", result["company"])
				}
				if result["description"] != "Developed software applications using Go and React" {
					t.Errorf("Expected description 'Developed software applications using Go and React', got '%v'", result["description"])
				}
				if result["id"] == nil {
					t.Error("Expected job ID to be set")
				}
			},
		},
		{
			name: "Missing required title",
			requestBody: `{
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name: "Missing required company",
			requestBody: `{
				"title": "Software Engineer",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name: "Missing required description",
			requestBody: `{
				"title": "Software Engineer",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name: "Title too long",
			requestBody: `{
				"title": "` + strings.Repeat("a", 201) + `",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			requestBody:    `{"title": "Software Engineer", "company":}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, rec := createEchoContext("POST", "/api/v1/jobs", bytes.NewBufferString(tt.requestBody), nil)

			err := handler.CreateJob(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_CreateJobDescription(t *testing.T) {
	mockJobService := mocks.NewMockJobService()
	mockProcessingService := mocks.NewMockProcessingService()
	handler := NewJobHandler(mockJobService, mockProcessingService)

	tests := []struct {
		name           string
		jobID          string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name:  "Valid description update",
			jobID: "job_123",
			requestBody: `{
				"description": "Updated description with new responsibilities"
			}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response SuccessResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal success response: %v", err)
				}
				if response.Message != "Description updated successfully" {
					t.Errorf("Expected 'Description updated successfully', got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Missing description",
			jobID: "job_123",
			requestBody: `{
				"description": ""
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Description too long",
			jobID: "job_123",
			requestBody: `{
				"description": "` + strings.Repeat("a", 5001) + `"
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Job not found",
			jobID: "nonexistent_job",
			requestBody: `{
				"description": "Valid description"
			}`,
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Job not found" {
					t.Errorf("Expected 'Job not found', got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			jobID:          "job_123",
			requestBody:    `{"description": "Valid description",}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pathParams := map[string]string{"id": tt.jobID}
			c, rec := createEchoContext("POST", "/api/v1/jobs/"+tt.jobID+"/description", bytes.NewBufferString(tt.requestBody), pathParams)

			err := handler.CreateJobDescription(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_GetJobs(t *testing.T) {
	mockJobService := mocks.NewMockJobService()
	mockProcessingService := mocks.NewMockProcessingService()
	handler := NewJobHandler(mockJobService, mockProcessingService)

	tests := []struct {
		name           string
		mockError      bool
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name:           "Successful retrieval",
			mockError:      false,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response models.JobListResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if response.Total != 2 {
					t.Errorf("Expected 2 jobs, got %d", response.Total)
				}
				if len(response.Jobs) != 2 {
					t.Errorf("Expected 2 jobs in array, got %d", len(response.Jobs))
				}
			},
		},
		{
			name:           "Database error",
			mockError:      true,
			expectedStatus: http.StatusInternalServerError,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Failed to retrieve jobs" {
					t.Errorf("Expected 'Failed to retrieve jobs', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockJobService.SetError(tt.mockError)

			c, rec := createEchoContext("GET", "/api/v1/jobs", nil, nil)

			err := handler.GetJobs(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_ProcessMultipleJobs(t *testing.T) {
	mockJobService := mocks.NewMockJobService()
	mockProcessingService := mocks.NewMockProcessingService()
	handler := NewJobHandler(mockJobService, mockProcessingService)

	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "Valid processing request",
			requestBody: `{
				"jobIds": ["job_1", "job_2"],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response struct {
					ProcessedJobs []models.ProcessedJob `json:"processedJobs"`
					Total         int                   `json:"total"`
				}
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if response.Total != 2 {
					t.Errorf("Expected 2 processed jobs, got %d", response.Total)
				}
				if len(response.ProcessedJobs) != 2 {
					t.Errorf("Expected 2 processed jobs in array, got %d", len(response.ProcessedJobs))
				}
			},
		},
		{
			name: "Empty job IDs",
			requestBody: `{
				"jobIds": [],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "validation failed") {
					t.Errorf("Expected validation error, got '%s'", response.Message)
				}
			},
		},
		{
			name: "Job not found",
			requestBody: `{
				"jobIds": ["nonexistent_job"],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "jobs not found") {
					t.Errorf("Expected 'jobs not found' message, got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			requestBody:    `{"jobIds": ["job_1"], "model":}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, rec := createEchoContext("POST", "/api/v1/jobs/process", bytes.NewBufferString(tt.requestBody), nil)

			err := handler.ProcessMultipleJobs(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}
