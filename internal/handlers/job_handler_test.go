package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/eldon111/impactresume/internal/database"
	"github.com/eldon111/impactresume/internal/models"
	"github.com/eldon111/impactresume/internal/services"
	"github.com/labstack/echo/v4"
)

// MockDatabase implements database.JobRepository interface for testing
// Deprecated: Use database.MockJobRepository instead
type MockDatabase struct {
	jobs        map[string]*models.Job
	shouldError bool
}

// Helper function to create Echo context for testing
func createEchoContext(method, path string, body *bytes.Buffer, pathParams map[string]string) (echo.Context, *httptest.ResponseRecorder) {
	e := echo.New()

	var req *http.Request
	if body != nil {
		req = httptest.NewRequest(method, path, body)
	} else {
		req = httptest.NewRequest(method, path, nil)
	}

	if method == "POST" || method == "PUT" || method == "PATCH" {
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	}

	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// Set path parameters if provided
	if pathParams != nil {
		for key, value := range pathParams {
			c.SetParamNames(key)
			c.SetParamValues(value)
		}
	}

	// Mock user ID for testing (simulate authenticated user)
	c.Set("user_id", 1)

	return c, rec
}

// Helper function to handle Echo error responses in tests
func handleEchoTestResult(t *testing.T, err error, rec *httptest.ResponseRecorder, expectedStatus int, checkResponse func(t *testing.T, body []byte)) {
	if err != nil {
		// Echo returns HTTP errors, check if it's the expected error
		if echoErr, ok := err.(*echo.HTTPError); ok {
			if echoErr.Code != expectedStatus {
				t.Errorf("Expected status %d, got %d", expectedStatus, echoErr.Code)
			}
			// For error responses, create a mock response body for tests
			if checkResponse != nil {
				var errorResponse map[string]interface{}

				// Handle validation errors (arrays) vs simple string messages
				if validationErrors, ok := echoErr.Message.([]string); ok {
					// This is a validation error array
					errorResponse = map[string]interface{}{
						"error":   "Bad Request",
						"code":    echoErr.Code,
						"message": "Validation failed",
						"details": validationErrors,
					}
				} else {
					// This is a simple string message
					errorResponse = map[string]interface{}{
						"error":   "Bad Request",
						"code":    echoErr.Code,
						"message": echoErr.Message,
					}
				}

				if errorJSON, err := json.Marshal(errorResponse); err == nil {
					checkResponse(t, errorJSON)
				}
			}
		} else {
			t.Errorf("Expected Echo HTTP error, got %v", err)
		}
	} else {
		// No error, check response status
		if rec.Code != expectedStatus {
			t.Errorf("Expected status %d, got %d", expectedStatus, rec.Code)
		}
		if checkResponse != nil {
			checkResponse(t, rec.Body.Bytes())
		}
	}
}

func NewMockDatabase() *MockDatabase {
	return &MockDatabase{
		jobs:        make(map[string]*models.Job),
		shouldError: false,
	}
}

func (m *MockDatabase) CreateJob(ctx context.Context, job *models.Job) error {
	if m.shouldError {
		return database.ErrJobNotFound // Use any error for testing
	}
	m.jobs[job.ID] = job
	return nil
}

func (m *MockDatabase) GetJobByID(ctx context.Context, userID int, id string) (*models.Job, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	job, exists := m.jobs[id]
	if !exists || job.UserID != userID {
		return nil, database.ErrJobNotFound
	}
	return job, nil
}

func (m *MockDatabase) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound
	}
	jobs := make([]models.Job, 0, len(m.jobs))
	for _, job := range m.jobs {
		if job.UserID == userID {
			jobs = append(jobs, *job)
		}
	}
	return jobs, nil
}

func (m *MockDatabase) UpdateJobDescription(ctx context.Context, userID int, id, description string) error {
	if m.shouldError {
		return database.ErrJobNotFound
	}
	job, exists := m.jobs[id]
	if !exists || job.UserID != userID {
		return database.ErrJobNotFound
	}
	job.Description = description
	job.UpdatedAt = time.Now()
	return nil
}

func (m *MockDatabase) DeleteJob(ctx context.Context, userID int, id string) error {
	if m.shouldError {
		return database.ErrJobNotFound
	}
	job, exists := m.jobs[id]
	if !exists || job.UserID != userID {
		return database.ErrJobNotFound
	}
	delete(m.jobs, id)
	return nil
}

func (m *MockDatabase) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// MockAIService implements services.AIServiceInterface for testing
type MockAIService struct {
	shouldError bool
}

func NewMockAIService() *MockAIService {
	return &MockAIService{shouldError: false}
}

func (m *MockAIService) CondenseJobHistory(ctx context.Context, title, company, description string) (*services.JobCondensationResult, error) {
	if m.shouldError {
		return nil, database.ErrJobNotFound // Use any error for testing
	}
	return &services.JobCondensationResult{
		BulletPoints: []string{"Enhanced software development process", "Improved team collaboration"},
		Model:        "claude-3-haiku",
		TokensUsed:   150,
	}, nil
}

func (m *MockAIService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

func TestJobHandler_CreateJob(t *testing.T) {
	mockDB := NewMockDatabase()
	mockAI := NewMockAIService()
	handler := NewJobHandler(mockDB, mockAI)

	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "Valid job creation",
			requestBody: `{
				"title": "Software Engineer",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications using Go and React",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusCreated,
			checkResponse: func(t *testing.T, body []byte) {
				// Parse as generic JSON to avoid DateOnly parsing issues in tests
				var result map[string]interface{}
				if err := json.Unmarshal(body, &result); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
					return
				}
				if result["title"] != "Software Engineer" {
					t.Errorf("Expected title 'Software Engineer', got '%v'", result["title"])
				}
				if result["company"] != "Tech Corp" {
					t.Errorf("Expected company 'Tech Corp', got '%v'", result["company"])
				}
				if result["description"] != "Developed software applications using Go and React" {
					t.Errorf("Expected description 'Developed software applications using Go and React', got '%v'", result["description"])
				}
				if result["id"] == nil {
					t.Error("Expected job ID to be set")
				}
			},
		},
		{
			name: "Missing required title",
			requestBody: `{
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
				if len(response.Details) == 0 {
					t.Error("Expected validation details")
				}
			},
		},
		{
			name: "Missing required company",
			requestBody: `{
				"title": "Software Engineer",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name: "Missing required description",
			requestBody: `{
				"title": "Software Engineer",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name: "Title too long",
			requestBody: `{
				"title": "` + strings.Repeat("a", 201) + `",
				"company": "Tech Corp",
				"location": "San Francisco, CA",
				"startDate": "2023-01-15",
				"description": "Developed software applications",
				"isCurrent": true
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			requestBody:    `{"title": "Software Engineer", "company":}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, rec := createEchoContext("POST", "/api/v1/jobs", bytes.NewBufferString(tt.requestBody), nil)

			err := handler.CreateJob(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_CreateJobDescription(t *testing.T) {
	mockDB := NewMockDatabase()
	mockAI := NewMockAIService()
	handler := NewJobHandler(mockDB, mockAI)

	// Add a job to the mock database
	job := &models.Job{
		ID:          "job_123",
		UserID:      1,
		Title:       "Software Engineer",
		Company:     "Tech Corp",
		Description: "Old description",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	mockDB.jobs[job.ID] = job

	tests := []struct {
		name           string
		jobID          string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name:  "Valid description update",
			jobID: "job_123",
			requestBody: `{
				"description": "Updated description with new responsibilities"
			}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response SuccessResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal success response: %v", err)
				}
				if response.Message != "Description updated successfully" {
					t.Errorf("Expected 'Description updated successfully', got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Missing description",
			jobID: "job_123",
			requestBody: `{
				"description": ""
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Description too long",
			jobID: "job_123",
			requestBody: `{
				"description": "` + strings.Repeat("a", 5001) + `"
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name:  "Job not found",
			jobID: "nonexistent_job",
			requestBody: `{
				"description": "Valid description"
			}`,
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Job not found" {
					t.Errorf("Expected 'Job not found', got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			jobID:          "job_123",
			requestBody:    `{"description": "Valid description",}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pathParams := map[string]string{"id": tt.jobID}
			c, rec := createEchoContext("POST", "/api/v1/jobs/"+tt.jobID+"/description", bytes.NewBufferString(tt.requestBody), pathParams)

			err := handler.CreateJobDescription(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_GetJobs(t *testing.T) {
	mockDB := NewMockDatabase()
	mockAI := NewMockAIService()
	handler := NewJobHandler(mockDB, mockAI)

	// Add some jobs to the mock database
	job1 := &models.Job{
		ID:      "job_1",
		UserID:  1,
		Title:   "Software Engineer",
		Company: "Tech Corp",
	}
	job2 := &models.Job{
		ID:      "job_2",
		UserID:  1,
		Title:   "Product Manager",
		Company: "Business Inc",
	}
	mockDB.jobs[job1.ID] = job1
	mockDB.jobs[job2.ID] = job2

	tests := []struct {
		name           string
		mockError      bool
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name:           "Successful retrieval",
			mockError:      false,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response models.JobListResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if response.Total != 2 {
					t.Errorf("Expected 2 jobs, got %d", response.Total)
				}
				if len(response.Jobs) != 2 {
					t.Errorf("Expected 2 jobs in array, got %d", len(response.Jobs))
				}
			},
		},
		{
			name:           "Database error",
			mockError:      true,
			expectedStatus: http.StatusInternalServerError,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Failed to retrieve jobs" {
					t.Errorf("Expected 'Failed to retrieve jobs', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB.SetError(tt.mockError)

			c, rec := createEchoContext("GET", "/api/v1/jobs", nil, nil)

			err := handler.GetJobs(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}

func TestJobHandler_ProcessMultipleJobs(t *testing.T) {
	mockDB := NewMockDatabase()
	mockAI := NewMockAIService()
	handler := NewJobHandler(mockDB, mockAI)

	// Add jobs to the mock database
	job1 := &models.Job{
		ID:          "job_1",
		UserID:      1,
		Title:       "Software Engineer",
		Company:     "Tech Corp",
		Description: "Developed software applications",
	}
	job2 := &models.Job{
		ID:          "job_2",
		UserID:      1,
		Title:       "Product Manager",
		Company:     "Business Inc",
		Description: "Managed product development",
	}
	mockDB.jobs[job1.ID] = job1
	mockDB.jobs[job2.ID] = job2

	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "Valid processing request",
			requestBody: `{
				"jobIds": ["job_1", "job_2"],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response struct {
					ProcessedJobs []models.ProcessedJob `json:"processedJobs"`
					Total         int                   `json:"total"`
				}
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}
				if response.Total != 2 {
					t.Errorf("Expected 2 processed jobs, got %d", response.Total)
				}
				if len(response.ProcessedJobs) != 2 {
					t.Errorf("Expected 2 processed jobs in array, got %d", len(response.ProcessedJobs))
				}
			},
		},
		{
			name: "Empty job IDs",
			requestBody: `{
				"jobIds": [],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Validation failed" {
					t.Errorf("Expected 'Validation failed', got '%s'", response.Message)
				}
			},
		},
		{
			name: "Job not found",
			requestBody: `{
				"jobIds": ["nonexistent_job"],
				"model": "claude-3-haiku"
			}`,
			expectedStatus: http.StatusNotFound,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if !strings.Contains(response.Message, "Job not found") {
					t.Errorf("Expected 'Job not found' message, got '%s'", response.Message)
				}
			},
		},
		{
			name:           "Invalid JSON",
			requestBody:    `{"jobIds": ["job_1"], "model":}`,
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				var response ErrorResponse
				if err := json.Unmarshal(body, &response); err != nil {
					t.Errorf("Failed to unmarshal error response: %v", err)
				}
				if response.Message != "Invalid JSON request" {
					t.Errorf("Expected 'Invalid JSON request', got '%s'", response.Message)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, rec := createEchoContext("POST", "/api/v1/jobs/process", bytes.NewBufferString(tt.requestBody), nil)

			err := handler.ProcessMultipleJobs(c)
			handleEchoTestResult(t, err, rec, tt.expectedStatus, tt.checkResponse)
		})
	}
}
